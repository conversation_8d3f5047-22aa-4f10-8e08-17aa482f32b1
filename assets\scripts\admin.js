// Admin Dashboard JavaScript
console.log('Admin script loaded');

// Global variables
let currentSection = 'dashboard';
let isAuthenticated = false;
let sidebarCollapsed = false;

// Initialize when DOM is ready
function initAdminDashboard() {
    console.log('Initializing admin dashboard...');
    bindEvents();
    checkAuthentication();
    setTimeout(initCharts, 1000);
}

// Bind all event listeners
function bindEvents() {
    console.log('Binding events...');

    // Login form
    const loginForm = document.getElementById('adminLoginForm');
    if (loginForm) {
        console.log('Login form found, binding submit event');
        loginForm.addEventListener('submit', handleLogin);

        // Also add click event to the button as backup
        const loginBtn = document.querySelector('.admin-login-btn');
        if (loginBtn) {
            console.log('Login button found, adding click handler');
            loginBtn.addEventListener('click', function(e) {
                if (e.target.type !== 'submit') {
                    e.preventDefault();
                    handleLogin(e);
                }
            });
        }
    } else {
        console.error('Login form not found!');
    }

    // Sidebar toggle
    const sidebarToggle = document.getElementById('sidebarToggle');
    if (sidebarToggle) {
        sidebarToggle.addEventListener('click', toggleSidebar);
    }

    // Navigation links
    const navLinks = document.querySelectorAll('.admin-nav-link');
    navLinks.forEach(link => {
        link.addEventListener('click', handleNavigation);
    });

    // Logout
    const logoutBtn = document.getElementById('adminLogout');
    if (logoutBtn) {
        logoutBtn.addEventListener('click', handleLogout);
    }

    // Password toggle
    const passwordToggles = document.querySelectorAll('.toggle-password');
    passwordToggles.forEach(toggle => {
        toggle.addEventListener('click', togglePassword);
    });

    // Add menu item modal
    const addMenuItemBtn = document.getElementById('addMenuItem');
    if (addMenuItemBtn) {
        addMenuItemBtn.addEventListener('click', showAddMenuItemModal);
    }

    // Modal close buttons
    const modalCloses = document.querySelectorAll('.modal-close, .modal-cancel');
    modalCloses.forEach(close => {
        close.addEventListener('click', closeModal);
    });

    // Category tabs
    const categoryTabs = document.querySelectorAll('.category-tab');
    categoryTabs.forEach(tab => {
        tab.addEventListener('click', handleCategoryFilter);
    });

    // Settings navigation
    const settingsNavLinks = document.querySelectorAll('.settings-nav-link');
    settingsNavLinks.forEach(link => {
        link.addEventListener('click', handleSettingsNavigation);
    });

    // File upload
    const fileInputs = document.querySelectorAll('input[type="file"]');
    fileInputs.forEach(input => {
        input.addEventListener('change', handleFileUpload);
    });

    // Search functionality
    const searchInputs = document.querySelectorAll('.search-input');
    searchInputs.forEach(input => {
        input.addEventListener('input', handleSearch);
    });

    // Filter functionality
    const filterSelects = document.querySelectorAll('.filter-select');
    filterSelects.forEach(select => {
        select.addEventListener('change', handleFilter);
    });

    // Action buttons
    bindActionButtons();

    // Pagination
    bindPagination();
}

// Check authentication status
function checkAuthentication() {
    console.log('Checking authentication...');
    const authToken = localStorage.getItem('adminAuthToken');
    console.log('Auth token found:', authToken);

    if (authToken) {
        console.log('User already authenticated');
        isAuthenticated = true;
        showDashboard();
    } else {
        console.log('User not authenticated, showing login modal');
        showLoginModal();
    }
}

// Handle login form submission
function handleLogin(e) {
    e.preventDefault();
    console.log('Login form submitted');

    const emailInput = document.getElementById('admin-email');
    const passwordInput = document.getElementById('admin-password');

    if (!emailInput || !passwordInput) {
        console.error('Email or password input not found');
        alert('Form error: Input fields not found');
        return;
    }

    const email = emailInput.value.trim();
    const password = passwordInput.value.trim();
    const loginBtn = document.querySelector('.admin-login-btn');

    console.log(`Login attempt with email: "${email}"`);
    console.log(`Password length: ${password.length}`);

    if (!email || !password) {
        alert('Please enter both email and password');
        return;
    }

    // Show loading state
    if (loginBtn) {
        loginBtn.classList.add('loading');
        loginBtn.disabled = true;
    }

    // Use setTimeout to simulate async behavior and show loading state
    setTimeout(() => {
        try {
            console.log('Checking credentials...');
            console.log(`Expected: "<EMAIL>", Got: "${email}"`);
            console.log(`Expected: "admin123", Got: "${password}"`);

            // Direct check for simplicity
            if (email === '<EMAIL>' && password === 'admin123') {
                console.log('Login successful - credentials match');

                // Clear any existing token first
                localStorage.removeItem('adminAuthToken');

                // Store new auth token
                localStorage.setItem('adminAuthToken', 'dummy-token-' + Date.now());
                console.log('Auth token stored:', localStorage.getItem('adminAuthToken'));

                isAuthenticated = true;

                // Clear form
                emailInput.value = '';
                passwordInput.value = '';

                hideLoginModal();
                showDashboard();
            } else {
                console.log('Invalid credentials - no match');
                alert('Invalid credentials. Please use:\nEmail: <EMAIL>\nPassword: admin123');
            }
        } catch (error) {
            console.error('Login error:', error);
            alert('An error occurred during login. Please try again.');
        } finally {
            if (loginBtn) {
                loginBtn.classList.remove('loading');
                loginBtn.disabled = false;
            }
        }
    }, 500); // Small delay to show loading state
}

    simulateLogin(email, password) {
        return new Promise((resolve, reject) => {
            setTimeout(() => {
                // Demo credentials
                if (email === '<EMAIL>' && password === 'admin123') {
                    resolve();
                } else {
                    reject(new Error('Invalid credentials'));
                }
            }, 1500);
        });
    }

// Show login modal
function showLoginModal() {
    console.log('Showing login modal');
    const modal = document.getElementById('adminLoginModal');
    if (modal) {
        modal.classList.add('active');
        console.log('Login modal shown');
    } else {
        console.error('Login modal not found');
    }
}

// Hide login modal
function hideLoginModal() {
    console.log('Hiding login modal');
    const modal = document.getElementById('adminLoginModal');
    if (modal) {
        modal.classList.remove('active');
        console.log('Login modal hidden');
    } else {
        console.error('Login modal not found');
    }
}

// Show dashboard
function showDashboard() {
    console.log('Showing dashboard');
    const dashboard = document.getElementById('adminDashboard');
    if (dashboard) {
        dashboard.classList.add('active');
        console.log('Dashboard shown');
    } else {
        console.error('Dashboard not found');
    }
}

// Handle logout
function handleLogout(e) {
    if (e) e.preventDefault();
    console.log('Logging out...');

    // Clear authentication
    localStorage.removeItem('adminAuthToken');
    console.log('Auth token removed from localStorage');

    isAuthenticated = false;

    // Hide dashboard
    const dashboard = document.getElementById('adminDashboard');
    if (dashboard) {
        dashboard.classList.remove('active');
        console.log('Dashboard hidden');
    } else {
        console.error('Dashboard element not found');
    }

    // Show login modal
    showLoginModal();
    console.log('Logout complete');
}

// Toggle sidebar
function toggleSidebar() {
    const sidebar = document.getElementById('adminSidebar');
    const main = document.querySelector('.admin-main');

    sidebarCollapsed = !sidebarCollapsed;

    if (sidebarCollapsed) {
        sidebar.classList.add('collapsed');
        main.classList.add('expanded');
    } else {
        sidebar.classList.remove('collapsed');
        main.classList.remove('expanded');
    }
}

// Handle navigation
function handleNavigation(e) {
    e.preventDefault();

    const link = e.currentTarget;
    const section = link.dataset.section;

    if (section) {
        switchSection(section);

        // Update active nav link
        document.querySelectorAll('.admin-nav-link').forEach(l => l.classList.remove('active'));
        link.classList.add('active');
    }
}

// Switch sections
function switchSection(sectionName) {
    // Hide all sections
    document.querySelectorAll('.admin-section').forEach(section => {
        section.classList.remove('active');
    });

    // Show target section
    const targetSection = document.getElementById(`${sectionName}-section`);
    if (targetSection) {
        targetSection.classList.add('active');
        currentSection = sectionName;
    }
}

// Toggle password visibility
function togglePassword(e) {
    const button = e.currentTarget;
    const input = button.parentElement.querySelector('input');
    const icon = button.querySelector('i');

    if (input.type === 'password') {
        input.type = 'text';
        icon.classList.remove('fa-eye');
        icon.classList.add('fa-eye-slash');
    } else {
        input.type = 'password';
        icon.classList.remove('fa-eye-slash');
        icon.classList.add('fa-eye');
    }
}

// Show add menu item modal
function showAddMenuItemModal() {
    console.log('Show add menu item modal');
    const modal = document.getElementById('addMenuItemModal');
    if (modal) {
        modal.classList.add('active');
        console.log('Add menu item modal shown');
    } else {
        console.error('Add menu item modal not found');
        alert('Add Menu Item functionality - Modal would open here');
    }
}

// Close modal
function closeModal(e) {
    e.preventDefault();
    const modal = e.currentTarget.closest('.admin-modal');
    if (modal) {
        modal.classList.remove('active');
        console.log('Modal closed');
    }
}

// Handle category filter
function handleCategoryFilter(e) {
    e.preventDefault();
    const category = e.target.dataset.category;
    console.log('Category filter:', category);

    // Update active tab
    document.querySelectorAll('.category-tab').forEach(tab => tab.classList.remove('active'));
    e.target.classList.add('active');

    // Filter items (demo functionality)
    alert(`Filtering menu items by category: ${category}`);
}

// Handle settings navigation
function handleSettingsNavigation(e) {
    e.preventDefault();
    const link = e.currentTarget;
    const targetId = link.getAttribute('href').substring(1);

    console.log('Settings navigation to:', targetId);

    // Update active nav link
    document.querySelectorAll('.settings-nav-link').forEach(l => l.classList.remove('active'));
    link.classList.add('active');

    // Show target panel
    document.querySelectorAll('.settings-panel').forEach(panel => {
        panel.classList.remove('active');
    });

    const targetPanel = document.getElementById(targetId);
    if (targetPanel) {
        targetPanel.classList.add('active');
        console.log(`Settings panel ${targetId} activated`);
    } else {
        alert(`Settings panel: ${targetId}`);
    }
}

// Handle file upload
function handleFileUpload(e) {
    console.log('File upload');
    const file = e.target.files[0];
    if (file) {
        console.log('File selected:', file.name);
        const preview = e.target.parentElement.querySelector('.file-preview');
        if (preview && file.type.startsWith('image/')) {
            const reader = new FileReader();
            reader.onload = function(e) {
                preview.innerHTML = `<img src="${e.target.result}" alt="Preview" style="max-width: 200px; max-height: 200px; border-radius: 8px;">`;
            };
            reader.readAsDataURL(file);
        }
        alert(`File selected: ${file.name}`);
    }
}

// Handle search
function handleSearch(e) {
    const query = e.target.value.toLowerCase();
    console.log('Search:', query);
    if (query.length > 2) {
        alert(`Searching for: ${query}`);
    }
}

// Handle filter
function handleFilter(e) {
    const filter = e.target.value;
    console.log('Filter:', filter);
    alert(`Filtering by: ${filter}`);
}
function bindActionButtons() {
    console.log('Binding action buttons');

    // View buttons
    document.querySelectorAll('.view-btn').forEach(btn => {
        btn.addEventListener('click', (e) => {
            e.preventDefault();
            console.log('View action clicked');
            alert('View functionality - This would open a detailed view');
        });
    });

    // Edit buttons
    document.querySelectorAll('.edit-btn').forEach(btn => {
        btn.addEventListener('click', (e) => {
            e.preventDefault();
            console.log('Edit action clicked');
            alert('Edit functionality - This would open an edit form');
        });
    });

    // Delete buttons
    document.querySelectorAll('.delete-btn').forEach(btn => {
        btn.addEventListener('click', (e) => {
            e.preventDefault();
            if (confirm('Are you sure you want to delete this item?')) {
                console.log('Delete action confirmed');
                alert('Delete functionality - Item would be deleted');
            }
        });
    });

    // Toggle buttons (for menu items)
    document.querySelectorAll('.toggle-btn').forEach(btn => {
        btn.addEventListener('click', (e) => {
            e.preventDefault();
            console.log('Toggle action clicked');
            const icon = btn.querySelector('i');
            if (icon.classList.contains('fa-eye')) {
                icon.classList.remove('fa-eye');
                icon.classList.add('fa-eye-slash');
                btn.title = 'Show Item';
                alert('Item hidden');
            } else {
                icon.classList.remove('fa-eye-slash');
                icon.classList.add('fa-eye');
                btn.title = 'Hide Item';
                alert('Item shown');
            }
        });
    });

    // Message buttons (for customers)
    document.querySelectorAll('.message-btn').forEach(btn => {
        btn.addEventListener('click', (e) => {
            e.preventDefault();
            console.log('Message action clicked');
            alert('Message functionality - This would open a messaging interface');
        });
    });
}

function bindPagination() {
    console.log('Binding pagination');

    // Pagination numbers
    document.querySelectorAll('.pagination-number').forEach(btn => {
        btn.addEventListener('click', (e) => {
            e.preventDefault();
            // Remove active from all
            document.querySelectorAll('.pagination-number').forEach(b => b.classList.remove('active'));
            // Add active to clicked
            e.target.classList.add('active');
            console.log(`Page ${e.target.textContent} clicked`);
            alert(`Loading page ${e.target.textContent}`);
        });
    });

    // Previous/Next buttons
    document.querySelectorAll('.pagination-btn').forEach(btn => {
        btn.addEventListener('click', (e) => {
            e.preventDefault();
            if (!btn.disabled) {
                const isNext = btn.classList.contains('next-btn');
                console.log(`${isNext ? 'Next' : 'Previous'} page clicked`);
                alert(`${isNext ? 'Next' : 'Previous'} page functionality`);
            }
        });
    });
}

// Initialize charts
function initCharts() {
    console.log('Initializing charts...');
    // Charts will be initialized when Chart.js is available
    if (typeof Chart !== 'undefined') {
        createSalesChart();
        createCategoriesChart();
        createDemographicsChart();
    } else {
        console.log('Chart.js not loaded yet, skipping chart initialization');
    }
}

// Create sales chart
function createSalesChart() {
    const salesChartEl = document.getElementById('salesChart');
    if (!salesChartEl) return;

    const ctx = salesChartEl.getContext('2d');

    const data = {
        labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'],
        datasets: [{
            label: 'Revenue (₦)',
            data: [1200000, 1350000, 1100000, 1450000, 1300000, 1500000],
            backgroundColor: 'rgba(255, 122, 0, 0.2)',
            borderColor: 'rgba(255, 122, 0, 1)',
            borderWidth: 2,
            tension: 0.3,
            fill: true
        }]
    };

    new Chart(ctx, {
        type: 'line',
        data: data,
        options: {
            responsive: true,
            maintainAspectRatio: false
        }
    });
}

// Create categories chart
function createCategoriesChart() {
    const categoriesChartEl = document.getElementById('categoriesChart');
    if (!categoriesChartEl) return;

    const ctx = categoriesChartEl.getContext('2d');

    const data = {
        labels: ['Local Delights', 'International', 'Beverages', 'Desserts'],
        datasets: [{
            data: [45, 25, 20, 10],
            backgroundColor: [
                'rgba(255, 122, 0, 0.8)',
                'rgba(44, 62, 80, 0.8)',
                'rgba(52, 152, 219, 0.8)',
                'rgba(155, 89, 182, 0.8)'
            ]
        }]
    };

    new Chart(ctx, {
        type: 'doughnut',
        data: data,
        options: {
            responsive: true,
            maintainAspectRatio: false
        }
    });
}

// Create demographics chart
function createDemographicsChart() {
    const demographicsChartEl = document.getElementById('demographicsChart');
    if (!demographicsChartEl) return;

    const ctx = demographicsChartEl.getContext('2d');

    const data = {
        labels: ['18-24', '25-34', '35-44', '45-54', '55+'],
        datasets: [{
            label: 'Customers by Age Group',
            data: [15, 35, 25, 15, 10],
            backgroundColor: [
                'rgba(255, 122, 0, 0.8)',
                'rgba(44, 62, 80, 0.8)',
                'rgba(52, 152, 219, 0.8)',
                'rgba(155, 89, 182, 0.8)',
                'rgba(46, 204, 113, 0.8)'
            ]
        }]
    };

    new Chart(ctx, {
        type: 'bar',
        data: data,
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    beginAtZero: true
                }
            }
        }
    });
}

    handleLogout() {
        localStorage.removeItem('adminAuthToken');
        this.isAuthenticated = false;
        
        const dashboard = document.getElementById('adminDashboard');
        if (dashboard) {
            dashboard.classList.remove('active');
        }
        
        this.showLoginModal();
    }

    toggleSidebar() {
        const sidebar = document.getElementById('adminSidebar');
        const main = document.querySelector('.admin-main');
        
        this.sidebarCollapsed = !this.sidebarCollapsed;
        
        if (this.sidebarCollapsed) {
            sidebar.classList.add('collapsed');
            main.classList.add('expanded');
        } else {
            sidebar.classList.remove('collapsed');
            main.classList.remove('expanded');
        }
    }

    handleNavigation(e) {
        e.preventDefault();
        
        const link = e.currentTarget;
        const section = link.dataset.section;
        
        if (section) {
            this.switchSection(section);
            
            // Update active nav link
            document.querySelectorAll('.admin-nav-link').forEach(l => l.classList.remove('active'));
            link.classList.add('active');
        }
    }

    switchSection(sectionName) {
        // Hide all sections
        document.querySelectorAll('.admin-section').forEach(section => {
            section.classList.remove('active');
        });
        
        // Show target section
        const targetSection = document.getElementById(`${sectionName}-section`);
        if (targetSection) {
            targetSection.classList.add('active');
            this.currentSection = sectionName;
        }
    }

    togglePassword(e) {
        const button = e.currentTarget;
        const input = button.parentElement.querySelector('input');
        const icon = button.querySelector('i');
        
        if (input.type === 'password') {
            input.type = 'text';
            icon.classList.remove('fa-eye');
            icon.classList.add('fa-eye-slash');
        } else {
            input.type = 'password';
            icon.classList.remove('fa-eye-slash');
            icon.classList.add('fa-eye');
        }
    }

    showAddMenuItemModal() {
        const modal = document.getElementById('addMenuItemModal');
        if (modal) {
            modal.classList.add('active');
        }
    }

    closeModal(e) {
        const modal = e.currentTarget.closest('.admin-modal');
        if (modal) {
            modal.classList.remove('active');
        }
    }

    handleCategoryFilter(e) {
        const tab = e.currentTarget;
        const category = tab.dataset.category;
        
        // Update active tab
        document.querySelectorAll('.category-tab').forEach(t => t.classList.remove('active'));
        tab.classList.add('active');
        
        // Filter menu items (this would typically filter actual data)
        console.log(`Filtering by category: ${category}`);
    }

    handleSettingsNavigation(e) {
        e.preventDefault();
        
        const link = e.currentTarget;
        const targetId = link.getAttribute('href').substring(1);
        
        // Update active nav link
        document.querySelectorAll('.settings-nav-link').forEach(l => l.classList.remove('active'));
        link.classList.add('active');
        
        // Show target panel
        document.querySelectorAll('.settings-panel').forEach(panel => {
            panel.classList.remove('active');
        });
        
        const targetPanel = document.getElementById(targetId);
        if (targetPanel) {
            targetPanel.classList.add('active');
        }
    }

    handleFileUpload(e) {
        const file = e.target.files[0];
        if (file) {
            const preview = e.target.parentElement.querySelector('.file-preview');
            if (preview && file.type.startsWith('image/')) {
                const reader = new FileReader();
                reader.onload = function(e) {
                    preview.innerHTML = `<img src="${e.target.result}" alt="Preview" style="max-width: 200px; max-height: 200px; border-radius: 8px;">`;
                };
                reader.readAsDataURL(file);
            }
        }
    }

    handleSearch(e) {
        const query = e.target.value.toLowerCase();
        console.log(`Searching for: ${query}`);
        // Implement search functionality
    }

    handleFilter(e) {
        const filter = e.target.value;
        console.log(`Filtering by: ${filter}`);
        // Implement filter functionality
    }

    bindActionButtons() {
        // View buttons
        document.querySelectorAll('.view-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                console.log('View action clicked');
                // Implement view functionality
            });
        });

        // Edit buttons
        document.querySelectorAll('.edit-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                console.log('Edit action clicked');
                // Implement edit functionality
            });
        });

        // Delete buttons
        document.querySelectorAll('.delete-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                if (confirm('Are you sure you want to delete this item?')) {
                    console.log('Delete action confirmed');
                    // Implement delete functionality
                }
            });
        });
    }

    bindPagination() {
        document.querySelectorAll('.pagination-number').forEach(btn => {
            btn.addEventListener('click', (e) => {
                document.querySelectorAll('.pagination-number').forEach(b => b.classList.remove('active'));
                e.target.classList.add('active');
                console.log(`Page ${e.target.textContent} clicked`);
            });
        });
    }

    showError(message) {
        // Simple error display - in a real app, you'd use a proper notification system
        alert(message);
    }

    initCharts() {
        // Initialize charts when the analytics section is loaded
        setTimeout(() => {
            this.createSalesChart();
            this.createCategoriesChart();
            this.createDemographicsChart();
        }, 1000);
    }

    createSalesChart() {
        const salesChartEl = document.getElementById('salesChart');
        if (!salesChartEl) return;

        const ctx = salesChartEl.getContext('2d');

        // Sample data
        const labels = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
        const data = {
            labels: labels,
            datasets: [
                {
                    label: 'Revenue (₦)',
                    data: [1200000, 1350000, 1100000, 1450000, 1300000, 1500000, 1650000, 1800000, 1750000, 1900000, 2100000, 2300000],
                    backgroundColor: 'rgba(255, 122, 0, 0.2)',
                    borderColor: 'rgba(255, 122, 0, 1)',
                    borderWidth: 2,
                    tension: 0.3,
                    fill: true
                },
                {
                    label: 'Orders',
                    data: [120, 135, 110, 145, 130, 150, 165, 180, 175, 190, 210, 230],
                    backgroundColor: 'rgba(44, 62, 80, 0.2)',
                    borderColor: 'rgba(44, 62, 80, 1)',
                    borderWidth: 2,
                    tension: 0.3,
                    fill: true
                }
            ]
        };

        // Chart configuration
        const config = {
            type: 'line',
            data: data,
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            callback: function(value) {
                                if (this.chart.config.data.datasets[0].label.includes('₦')) {
                                    return '₦' + value.toLocaleString();
                                }
                                return value;
                            }
                        }
                    }
                },
                plugins: {
                    legend: {
                        position: 'top',
                    },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                let label = context.dataset.label || '';
                                if (label.includes('₦')) {
                                    return label + ': ₦' + context.parsed.y.toLocaleString();
                                }
                                return label + ': ' + context.parsed.y;
                            }
                        }
                    }
                }
            }
        };

        // Create chart
        new Chart(ctx, config);
    }

    createCategoriesChart() {
        const categoriesChartEl = document.getElementById('categoriesChart');
        if (!categoriesChartEl) return;

        const ctx = categoriesChartEl.getContext('2d');

        // Sample data
        const data = {
            labels: ['Local Delights', 'International', 'Beverages', 'Desserts'],
            datasets: [{
                data: [45, 25, 20, 10],
                backgroundColor: [
                    'rgba(255, 122, 0, 0.8)',
                    'rgba(44, 62, 80, 0.8)',
                    'rgba(52, 152, 219, 0.8)',
                    'rgba(155, 89, 182, 0.8)'
                ],
                borderColor: [
                    'rgba(255, 122, 0, 1)',
                    'rgba(44, 62, 80, 1)',
                    'rgba(52, 152, 219, 1)',
                    'rgba(155, 89, 182, 1)'
                ],
                borderWidth: 1
            }]
        };

        // Chart configuration
        const config = {
            type: 'doughnut',
            data: data,
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'right',
                    },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                const label = context.label || '';
                                const value = context.parsed || 0;
                                const total = context.dataset.data.reduce((a, b) => a + b, 0);
                                const percentage = Math.round((value / total) * 100);
                                return `${label}: ${percentage}% (${value} orders)`;
                            }
                        }
                    }
                }
            }
        };

        // Create chart
        new Chart(ctx, config);
    }

    createDemographicsChart() {
        const demographicsChartEl = document.getElementById('demographicsChart');
        if (!demographicsChartEl) return;

        const ctx = demographicsChartEl.getContext('2d');

        // Sample data
        const data = {
            labels: ['18-24', '25-34', '35-44', '45-54', '55+'],
            datasets: [{
                label: 'Customers by Age Group',
                data: [15, 35, 25, 15, 10],
                backgroundColor: [
                    'rgba(255, 122, 0, 0.8)',
                    'rgba(44, 62, 80, 0.8)',
                    'rgba(52, 152, 219, 0.8)',
                    'rgba(155, 89, 182, 0.8)',
                    'rgba(46, 204, 113, 0.8)'
                ],
                borderColor: [
                    'rgba(255, 122, 0, 1)',
                    'rgba(44, 62, 80, 1)',
                    'rgba(52, 152, 219, 1)',
                    'rgba(155, 89, 182, 1)',
                    'rgba(46, 204, 113, 1)'
                ],
                borderWidth: 1
            }]
        };

        // Chart configuration
        const config = {
            type: 'bar',
            data: data,
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            callback: function(value) {
                                return value + '%';
                            }
                        }
                    }
                },
                plugins: {
                    legend: {
                        display: false
                    },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                return `${context.parsed.y}% of customers`;
                            }
                        }
                    }
                }
            }
        };

        // Create chart
        new Chart(ctx, config);
    }
}

// Function to initialize dashboard functionality after login
function initDashboardFeatures() {
    console.log('Initializing dashboard features...');

    // Bind all the interactive elements
    bindDashboardEvents();

    // Initialize charts
    setTimeout(initCharts, 500);

    console.log('Dashboard features initialized');
}

// Bind all dashboard event listeners
function bindDashboardEvents() {
    console.log('Binding dashboard events...');

    // Sidebar toggle
    const sidebarToggle = document.getElementById('sidebarToggle');
    if (sidebarToggle) {
        console.log('Binding sidebar toggle');
        sidebarToggle.addEventListener('click', toggleSidebar);
    }

    // Navigation links
    const navLinks = document.querySelectorAll('.admin-nav-link');
    console.log(`Found ${navLinks.length} navigation links`);
    navLinks.forEach(link => {
        link.addEventListener('click', handleNavigation);
    });

    // Logout button
    const logoutBtn = document.getElementById('adminLogout');
    if (logoutBtn) {
        console.log('Binding logout button');
        logoutBtn.addEventListener('click', handleLogout);
    }

    // Password toggle buttons
    const passwordToggles = document.querySelectorAll('.toggle-password');
    passwordToggles.forEach(toggle => {
        toggle.addEventListener('click', togglePassword);
    });

    // Add menu item button
    const addMenuItemBtn = document.getElementById('addMenuItem');
    if (addMenuItemBtn) {
        console.log('Binding add menu item button');
        addMenuItemBtn.addEventListener('click', showAddMenuItemModal);
    }

    // Modal close buttons
    const modalCloses = document.querySelectorAll('.modal-close, .modal-cancel');
    modalCloses.forEach(close => {
        close.addEventListener('click', closeModal);
    });

    // Category tabs
    const categoryTabs = document.querySelectorAll('.category-tab');
    categoryTabs.forEach(tab => {
        tab.addEventListener('click', handleCategoryFilter);
    });

    // Settings navigation
    const settingsNavLinks = document.querySelectorAll('.settings-nav-link');
    settingsNavLinks.forEach(link => {
        link.addEventListener('click', handleSettingsNavigation);
    });

    // File upload inputs
    const fileInputs = document.querySelectorAll('input[type="file"]');
    fileInputs.forEach(input => {
        input.addEventListener('change', handleFileUpload);
    });

    // Search inputs
    const searchInputs = document.querySelectorAll('.search-input');
    searchInputs.forEach(input => {
        input.addEventListener('input', handleSearch);
    });

    // Filter selects
    const filterSelects = document.querySelectorAll('.filter-select');
    filterSelects.forEach(select => {
        select.addEventListener('change', handleFilter);
    });

    // Action buttons
    bindActionButtons();

    // Pagination
    bindPagination();

    // User menu dropdown
    bindUserMenu();

    console.log('All dashboard events bound successfully');
}

// Bind user menu functionality
function bindUserMenu() {
    const userMenuToggle = document.querySelector('.user-menu-toggle');
    const userMenuDropdown = document.querySelector('.user-menu-dropdown');

    if (userMenuToggle && userMenuDropdown) {
        userMenuToggle.addEventListener('click', function(e) {
            e.stopPropagation();
            userMenuDropdown.style.opacity = userMenuDropdown.style.opacity === '1' ? '0' : '1';
            userMenuDropdown.style.visibility = userMenuDropdown.style.visibility === 'visible' ? 'hidden' : 'visible';
        });

        // Close dropdown when clicking outside
        document.addEventListener('click', function() {
            userMenuDropdown.style.opacity = '0';
            userMenuDropdown.style.visibility = 'hidden';
        });
    }
}

// Initialize when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
    console.log('DOM loaded, checking if dashboard is visible...');

    // Check if dashboard is already visible (user already logged in)
    const dashboard = document.getElementById('adminDashboard');
    if (dashboard && dashboard.classList.contains('active')) {
        console.log('Dashboard is visible, initializing features');
        initDashboardFeatures();
    } else {
        console.log('Dashboard not visible yet, waiting for login');
    }
});

// Also check on window load as fallback
window.addEventListener('load', () => {
    setTimeout(() => {
        const dashboard = document.getElementById('adminDashboard');
        if (dashboard && dashboard.classList.contains('active')) {
            console.log('Dashboard visible on window load, ensuring features are initialized');
            initDashboardFeatures();
        }
    }, 1000);
});
