// Admin Dashboard JavaScript

class AdminDashboard {
    constructor() {
        this.currentSection = 'dashboard';
        this.isAuthenticated = false;
        this.sidebarCollapsed = false;
        
        this.init();
    }

    init() {
        this.bindEvents();
        this.checkAuthentication();
        this.initCharts();
    }

    bindEvents() {
        // Login form
        const loginForm = document.getElementById('adminLoginForm');
        if (loginForm) {
            loginForm.addEventListener('submit', this.handleLogin.bind(this));
        }

        // Sidebar toggle
        const sidebarToggle = document.getElementById('sidebarToggle');
        if (sidebarToggle) {
            sidebarToggle.addEventListener('click', this.toggleSidebar.bind(this));
        }

        // Navigation links
        const navLinks = document.querySelectorAll('.admin-nav-link');
        navLinks.forEach(link => {
            link.addEventListener('click', this.handleNavigation.bind(this));
        });

        // Logout
        const logoutBtn = document.getElementById('adminLogout');
        if (logoutBtn) {
            logoutBtn.addEventListener('click', this.handleLogout.bind(this));
        }

        // Password toggle
        const passwordToggles = document.querySelectorAll('.toggle-password');
        passwordToggles.forEach(toggle => {
            toggle.addEventListener('click', this.togglePassword.bind(this));
        });

        // Add menu item modal
        const addMenuItemBtn = document.getElementById('addMenuItem');
        if (addMenuItemBtn) {
            addMenuItemBtn.addEventListener('click', this.showAddMenuItemModal.bind(this));
        }

        // Modal close buttons
        const modalCloses = document.querySelectorAll('.modal-close, .modal-cancel');
        modalCloses.forEach(close => {
            close.addEventListener('click', this.closeModal.bind(this));
        });

        // Category tabs
        const categoryTabs = document.querySelectorAll('.category-tab');
        categoryTabs.forEach(tab => {
            tab.addEventListener('click', this.handleCategoryFilter.bind(this));
        });

        // Settings navigation
        const settingsNavLinks = document.querySelectorAll('.settings-nav-link');
        settingsNavLinks.forEach(link => {
            link.addEventListener('click', this.handleSettingsNavigation.bind(this));
        });

        // File upload
        const fileInputs = document.querySelectorAll('input[type="file"]');
        fileInputs.forEach(input => {
            input.addEventListener('change', this.handleFileUpload.bind(this));
        });

        // Search functionality
        const searchInputs = document.querySelectorAll('.search-input');
        searchInputs.forEach(input => {
            input.addEventListener('input', this.handleSearch.bind(this));
        });

        // Filter functionality
        const filterSelects = document.querySelectorAll('.filter-select');
        filterSelects.forEach(select => {
            select.addEventListener('change', this.handleFilter.bind(this));
        });

        // Action buttons
        this.bindActionButtons();

        // Pagination
        this.bindPagination();
    }

    checkAuthentication() {
        // Check if user is already authenticated (e.g., from localStorage)
        const authToken = localStorage.getItem('adminAuthToken');
        if (authToken) {
            this.isAuthenticated = true;
            this.showDashboard();
        } else {
            this.showLoginModal();
        }
    }

    async handleLogin(e) {
        e.preventDefault();
        
        const email = document.getElementById('admin-email').value;
        const password = document.getElementById('admin-password').value;
        const loginBtn = document.querySelector('.admin-login-btn');
        
        // Show loading state
        loginBtn.classList.add('loading');
        loginBtn.disabled = true;

        try {
            // Simulate API call
            await this.simulateLogin(email, password);
            
            // Store auth token
            localStorage.setItem('adminAuthToken', 'dummy-token');
            
            this.isAuthenticated = true;
            this.hideLoginModal();
            this.showDashboard();
            
        } catch (error) {
            this.showError('Invalid credentials. Please try again.');
        } finally {
            loginBtn.classList.remove('loading');
            loginBtn.disabled = false;
        }
    }

    simulateLogin(email, password) {
        return new Promise((resolve, reject) => {
            setTimeout(() => {
                // Demo credentials
                if (email === '<EMAIL>' && password === 'admin123') {
                    resolve();
                } else {
                    reject(new Error('Invalid credentials'));
                }
            }, 1500);
        });
    }

    showLoginModal() {
        const modal = document.getElementById('adminLoginModal');
        if (modal) {
            modal.classList.add('active');
        }
    }

    hideLoginModal() {
        const modal = document.getElementById('adminLoginModal');
        if (modal) {
            modal.classList.remove('active');
        }
    }

    showDashboard() {
        const dashboard = document.getElementById('adminDashboard');
        if (dashboard) {
            dashboard.classList.add('active');
        }
    }

    handleLogout() {
        localStorage.removeItem('adminAuthToken');
        this.isAuthenticated = false;
        
        const dashboard = document.getElementById('adminDashboard');
        if (dashboard) {
            dashboard.classList.remove('active');
        }
        
        this.showLoginModal();
    }

    toggleSidebar() {
        const sidebar = document.getElementById('adminSidebar');
        const main = document.querySelector('.admin-main');
        
        this.sidebarCollapsed = !this.sidebarCollapsed;
        
        if (this.sidebarCollapsed) {
            sidebar.classList.add('collapsed');
            main.classList.add('expanded');
        } else {
            sidebar.classList.remove('collapsed');
            main.classList.remove('expanded');
        }
    }

    handleNavigation(e) {
        e.preventDefault();
        
        const link = e.currentTarget;
        const section = link.dataset.section;
        
        if (section) {
            this.switchSection(section);
            
            // Update active nav link
            document.querySelectorAll('.admin-nav-link').forEach(l => l.classList.remove('active'));
            link.classList.add('active');
        }
    }

    switchSection(sectionName) {
        // Hide all sections
        document.querySelectorAll('.admin-section').forEach(section => {
            section.classList.remove('active');
        });
        
        // Show target section
        const targetSection = document.getElementById(`${sectionName}-section`);
        if (targetSection) {
            targetSection.classList.add('active');
            this.currentSection = sectionName;
        }
    }

    togglePassword(e) {
        const button = e.currentTarget;
        const input = button.parentElement.querySelector('input');
        const icon = button.querySelector('i');
        
        if (input.type === 'password') {
            input.type = 'text';
            icon.classList.remove('fa-eye');
            icon.classList.add('fa-eye-slash');
        } else {
            input.type = 'password';
            icon.classList.remove('fa-eye-slash');
            icon.classList.add('fa-eye');
        }
    }

    showAddMenuItemModal() {
        const modal = document.getElementById('addMenuItemModal');
        if (modal) {
            modal.classList.add('active');
        }
    }

    closeModal(e) {
        const modal = e.currentTarget.closest('.admin-modal');
        if (modal) {
            modal.classList.remove('active');
        }
    }

    handleCategoryFilter(e) {
        const tab = e.currentTarget;
        const category = tab.dataset.category;
        
        // Update active tab
        document.querySelectorAll('.category-tab').forEach(t => t.classList.remove('active'));
        tab.classList.add('active');
        
        // Filter menu items (this would typically filter actual data)
        console.log(`Filtering by category: ${category}`);
    }

    handleSettingsNavigation(e) {
        e.preventDefault();
        
        const link = e.currentTarget;
        const targetId = link.getAttribute('href').substring(1);
        
        // Update active nav link
        document.querySelectorAll('.settings-nav-link').forEach(l => l.classList.remove('active'));
        link.classList.add('active');
        
        // Show target panel
        document.querySelectorAll('.settings-panel').forEach(panel => {
            panel.classList.remove('active');
        });
        
        const targetPanel = document.getElementById(targetId);
        if (targetPanel) {
            targetPanel.classList.add('active');
        }
    }

    handleFileUpload(e) {
        const file = e.target.files[0];
        if (file) {
            const preview = e.target.parentElement.querySelector('.file-preview');
            if (preview && file.type.startsWith('image/')) {
                const reader = new FileReader();
                reader.onload = function(e) {
                    preview.innerHTML = `<img src="${e.target.result}" alt="Preview" style="max-width: 200px; max-height: 200px; border-radius: 8px;">`;
                };
                reader.readAsDataURL(file);
            }
        }
    }

    handleSearch(e) {
        const query = e.target.value.toLowerCase();
        console.log(`Searching for: ${query}`);
        // Implement search functionality
    }

    handleFilter(e) {
        const filter = e.target.value;
        console.log(`Filtering by: ${filter}`);
        // Implement filter functionality
    }

    bindActionButtons() {
        // View buttons
        document.querySelectorAll('.view-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                console.log('View action clicked');
                // Implement view functionality
            });
        });

        // Edit buttons
        document.querySelectorAll('.edit-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                console.log('Edit action clicked');
                // Implement edit functionality
            });
        });

        // Delete buttons
        document.querySelectorAll('.delete-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                if (confirm('Are you sure you want to delete this item?')) {
                    console.log('Delete action confirmed');
                    // Implement delete functionality
                }
            });
        });
    }

    bindPagination() {
        document.querySelectorAll('.pagination-number').forEach(btn => {
            btn.addEventListener('click', (e) => {
                document.querySelectorAll('.pagination-number').forEach(b => b.classList.remove('active'));
                e.target.classList.add('active');
                console.log(`Page ${e.target.textContent} clicked`);
            });
        });
    }

    showError(message) {
        // Simple error display - in a real app, you'd use a proper notification system
        alert(message);
    }

    initCharts() {
        // Initialize charts when the analytics section is loaded
        setTimeout(() => {
            this.createSalesChart();
            this.createCategoriesChart();
            this.createDemographicsChart();
        }, 1000);
    }

    createSalesChart() {
        const salesChartEl = document.getElementById('salesChart');
        if (!salesChartEl) return;

        const ctx = salesChartEl.getContext('2d');

        // Sample data
        const labels = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
        const data = {
            labels: labels,
            datasets: [
                {
                    label: 'Revenue (₦)',
                    data: [1200000, 1350000, 1100000, 1450000, 1300000, 1500000, 1650000, 1800000, 1750000, 1900000, 2100000, 2300000],
                    backgroundColor: 'rgba(255, 122, 0, 0.2)',
                    borderColor: 'rgba(255, 122, 0, 1)',
                    borderWidth: 2,
                    tension: 0.3,
                    fill: true
                },
                {
                    label: 'Orders',
                    data: [120, 135, 110, 145, 130, 150, 165, 180, 175, 190, 210, 230],
                    backgroundColor: 'rgba(44, 62, 80, 0.2)',
                    borderColor: 'rgba(44, 62, 80, 1)',
                    borderWidth: 2,
                    tension: 0.3,
                    fill: true
                }
            ]
        };

        // Chart configuration
        const config = {
            type: 'line',
            data: data,
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            callback: function(value) {
                                if (this.chart.config.data.datasets[0].label.includes('₦')) {
                                    return '₦' + value.toLocaleString();
                                }
                                return value;
                            }
                        }
                    }
                },
                plugins: {
                    legend: {
                        position: 'top',
                    },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                let label = context.dataset.label || '';
                                if (label.includes('₦')) {
                                    return label + ': ₦' + context.parsed.y.toLocaleString();
                                }
                                return label + ': ' + context.parsed.y;
                            }
                        }
                    }
                }
            }
        };

        // Create chart
        new Chart(ctx, config);
    }

    createCategoriesChart() {
        const categoriesChartEl = document.getElementById('categoriesChart');
        if (!categoriesChartEl) return;

        const ctx = categoriesChartEl.getContext('2d');

        // Sample data
        const data = {
            labels: ['Local Delights', 'International', 'Beverages', 'Desserts'],
            datasets: [{
                data: [45, 25, 20, 10],
                backgroundColor: [
                    'rgba(255, 122, 0, 0.8)',
                    'rgba(44, 62, 80, 0.8)',
                    'rgba(52, 152, 219, 0.8)',
                    'rgba(155, 89, 182, 0.8)'
                ],
                borderColor: [
                    'rgba(255, 122, 0, 1)',
                    'rgba(44, 62, 80, 1)',
                    'rgba(52, 152, 219, 1)',
                    'rgba(155, 89, 182, 1)'
                ],
                borderWidth: 1
            }]
        };

        // Chart configuration
        const config = {
            type: 'doughnut',
            data: data,
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'right',
                    },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                const label = context.label || '';
                                const value = context.parsed || 0;
                                const total = context.dataset.data.reduce((a, b) => a + b, 0);
                                const percentage = Math.round((value / total) * 100);
                                return `${label}: ${percentage}% (${value} orders)`;
                            }
                        }
                    }
                }
            }
        };

        // Create chart
        new Chart(ctx, config);
    }

    createDemographicsChart() {
        const demographicsChartEl = document.getElementById('demographicsChart');
        if (!demographicsChartEl) return;

        const ctx = demographicsChartEl.getContext('2d');

        // Sample data
        const data = {
            labels: ['18-24', '25-34', '35-44', '45-54', '55+'],
            datasets: [{
                label: 'Customers by Age Group',
                data: [15, 35, 25, 15, 10],
                backgroundColor: [
                    'rgba(255, 122, 0, 0.8)',
                    'rgba(44, 62, 80, 0.8)',
                    'rgba(52, 152, 219, 0.8)',
                    'rgba(155, 89, 182, 0.8)',
                    'rgba(46, 204, 113, 0.8)'
                ],
                borderColor: [
                    'rgba(255, 122, 0, 1)',
                    'rgba(44, 62, 80, 1)',
                    'rgba(52, 152, 219, 1)',
                    'rgba(155, 89, 182, 1)',
                    'rgba(46, 204, 113, 1)'
                ],
                borderWidth: 1
            }]
        };

        // Chart configuration
        const config = {
            type: 'bar',
            data: data,
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            callback: function(value) {
                                return value + '%';
                            }
                        }
                    }
                },
                plugins: {
                    legend: {
                        display: false
                    },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                return `${context.parsed.y}% of customers`;
                            }
                        }
                    }
                }
            }
        };

        // Create chart
        new Chart(ctx, config);
    }
}

// Initialize the admin dashboard when the DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    const adminDashboard = new AdminDashboard();
});
