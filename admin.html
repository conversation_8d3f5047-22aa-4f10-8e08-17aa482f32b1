<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-Content-Security-Policy" content="
        default-src 'self';
        script-src 'self' 'nonce-{RANDOM_NONCE}';
        style-src 'self' 'unsafe-inline';
        img-src 'self' data: https:;
        connect-src 'self' https://api.magicmenu.com;
        frame-ancestors 'none';
        form-action 'self';
    ">
    <meta name="csrf-token" content="{SERVER_GENERATED_TOKEN}">
    <title>Magic Menu - Admin Dashboard</title>
    <link rel="stylesheet" href="assets/css/styles.css">
    <link rel="stylesheet" href="assets/css/admin.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="apple-touch-icon" sizes="180x180" href="assets/images/apple-touch-icon.png">
    <link rel="icon" type="image/png" sizes="32x32" href="assets/images/favicon-32x32.png">
    <link rel="icon" type="image/png" sizes="16x16" href="assets/images/favicon-16x16.png">
    <link rel="manifest" href="assets/images/site.webmanifest">
</head>
<body class="admin-body">
    <!-- Admin Login Modal -->
    <div id="adminLoginModal" class="admin-modal active">
        <div class="admin-modal-content">
            <div class="admin-login-header">
                <img src="assets/images/logo.png" alt="Magic Menu Logo" class="admin-logo">
                <h2>Admin Access</h2>
                <p>Please enter your administrator credentials</p>
            </div>
            <form id="adminLoginForm" class="admin-login-form">
                <div class="form-group">
                    <label for="admin-email">
                        <i class="fas fa-user-shield"></i> Admin Email
                    </label>
                    <input type="email" id="admin-email" required>
                    <div class="error-message"></div>
                </div>
                <div class="form-group">
                    <label for="admin-password">
                        <i class="fas fa-lock"></i> Password
                    </label>
                    <input type="password" id="admin-password" required>
                    <button type="button" class="toggle-password">
                        <i class="fas fa-eye"></i>
                    </button>
                    <div class="error-message"></div>
                </div>
                <button type="submit" class="btn btn-primary admin-login-btn">
                    <span class="btn-text">Access Dashboard</span>
                    <span class="btn-loader"></span>
                </button>
            </form>
        </div>
    </div>

    <!-- Admin Dashboard -->
    <div id="adminDashboard" class="admin-dashboard">
        <!-- Admin Header -->
        <header class="admin-header">
            <div class="admin-header-content">
                <div class="admin-header-left">
                    <button class="sidebar-toggle" id="sidebarToggle">
                        <i class="fas fa-bars"></i>
                    </button>
                    <h1 class="admin-title">Magic Menu Admin</h1>
                </div>
                <div class="admin-header-right">
                    <div class="admin-notifications">
                        <button class="notification-btn">
                            <i class="fas fa-bell"></i>
                            <span class="notification-count">3</span>
                        </button>
                    </div>
                    <div class="admin-user-menu">
                        <button class="user-menu-toggle">
                            <div class="admin-avatar" style="background: var(--primary-color); color: white; display: flex; align-items: center; justify-content: center; font-weight: bold;">A</div>
                            <span class="admin-name">Administrator</span>
                            <i class="fas fa-chevron-down"></i>
                        </button>
                        <div class="user-menu-dropdown">
                            <a href="#" class="user-menu-item">
                                <i class="fas fa-user"></i> Profile
                            </a>
                            <a href="#" class="user-menu-item">
                                <i class="fas fa-cog"></i> Settings
                            </a>
                            <hr class="user-menu-divider">
                            <a href="#" class="user-menu-item" id="adminLogout">
                                <i class="fas fa-sign-out-alt"></i> Logout
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </header>

        <!-- Admin Sidebar -->
        <aside class="admin-sidebar" id="adminSidebar">
            <nav class="admin-nav">
                <ul class="admin-nav-list">
                    <li class="admin-nav-item">
                        <a href="#dashboard" class="admin-nav-link active" data-section="dashboard">
                            <i class="fas fa-tachometer-alt"></i>
                            <span>Dashboard</span>
                        </a>
                    </li>
                    <li class="admin-nav-item">
                        <a href="#orders" class="admin-nav-link" data-section="orders">
                            <i class="fas fa-shopping-bag"></i>
                            <span>Orders</span>
                            <span class="nav-badge">12</span>
                        </a>
                    </li>
                    <li class="admin-nav-item">
                        <a href="#menu" class="admin-nav-link" data-section="menu">
                            <i class="fas fa-utensils"></i>
                            <span>Menu Management</span>
                        </a>
                    </li>
                    <li class="admin-nav-item">
                        <a href="#customers" class="admin-nav-link" data-section="customers">
                            <i class="fas fa-users"></i>
                            <span>Customers</span>
                        </a>
                    </li>
                    <li class="admin-nav-item">
                        <a href="#analytics" class="admin-nav-link" data-section="analytics">
                            <i class="fas fa-chart-bar"></i>
                            <span>Analytics</span>
                        </a>
                    </li>
                    <li class="admin-nav-item">
                        <a href="#settings" class="admin-nav-link" data-section="settings">
                            <i class="fas fa-cog"></i>
                            <span>Settings</span>
                        </a>
                    </li>
                </ul>
            </nav>
        </aside>

        <!-- Main Content Area -->
        <main class="admin-main">
            <!-- Dashboard Section -->
            <section id="dashboard-section" class="admin-section active">
                <div class="admin-section-header">
                    <h2>Dashboard Overview</h2>
                    <div class="section-actions">
                        <button class="btn btn-secondary">
                            <i class="fas fa-download"></i> Export Report
                        </button>
                    </div>
                </div>

                <!-- Stats Cards -->
                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-shopping-bag"></i>
                        </div>
                        <div class="stat-content">
                            <h3 class="stat-number">247</h3>
                            <p class="stat-label">Total Orders</p>
                            <span class="stat-change positive">+12% from last month</span>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-naira-sign"></i>
                        </div>
                        <div class="stat-content">
                            <h3 class="stat-number">₦1,234,567</h3>
                            <p class="stat-label">Revenue</p>
                            <span class="stat-change positive">+8% from last month</span>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-users"></i>
                        </div>
                        <div class="stat-content">
                            <h3 class="stat-number">1,456</h3>
                            <p class="stat-label">Customers</p>
                            <span class="stat-change positive">+15% from last month</span>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-star"></i>
                        </div>
                        <div class="stat-content">
                            <h3 class="stat-number">4.8</h3>
                            <p class="stat-label">Avg Rating</p>
                            <span class="stat-change neutral">Same as last month</span>
                        </div>
                    </div>
                </div>

                <!-- Recent Orders & Popular Items -->
                <div class="dashboard-grid">
                    <!-- Recent Orders -->
                    <div class="dashboard-card">
                        <div class="dashboard-card-header">
                            <h3>Recent Orders</h3>
                            <a href="#orders" class="view-all">View All</a>
                        </div>
                        <div class="dashboard-card-content">
                            <table class="data-table">
                                <thead>
                                    <tr>
                                        <th>Order ID</th>
                                        <th>Customer</th>
                                        <th>Amount</th>
                                        <th>Status</th>
                                        <th>Date</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td>#ORD-2305</td>
                                        <td>John Doe</td>
                                        <td>₦12,500</td>
                                        <td><span class="status-badge completed">Completed</span></td>
                                        <td>Today, 10:45 AM</td>
                                    </tr>
                                    <tr>
                                        <td>#ORD-2304</td>
                                        <td>Sarah Johnson</td>
                                        <td>₦8,750</td>
                                        <td><span class="status-badge processing">Processing</span></td>
                                        <td>Today, 9:30 AM</td>
                                    </tr>
                                    <tr>
                                        <td>#ORD-2303</td>
                                        <td>Michael Brown</td>
                                        <td>₦15,200</td>
                                        <td><span class="status-badge completed">Completed</span></td>
                                        <td>Yesterday, 7:15 PM</td>
                                    </tr>
                                    <tr>
                                        <td>#ORD-2302</td>
                                        <td>Emily Wilson</td>
                                        <td>₦6,800</td>
                                        <td><span class="status-badge cancelled">Cancelled</span></td>
                                        <td>Yesterday, 5:20 PM</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>

                    <!-- Popular Items -->
                    <div class="dashboard-card">
                        <div class="dashboard-card-header">
                            <h3>Popular Items</h3>
                            <a href="#menu" class="view-all">Manage Menu</a>
                        </div>
                        <div class="dashboard-card-content">
                            <ul class="popular-items-list">
                                <li class="popular-item">
                                    <img src="assets/images/jollof-rice.jpg" alt="Jollof Rice" class="popular-item-image">
                                    <div class="popular-item-details">
                                        <h4>Jollof Rice</h4>
                                        <p>₦2,500</p>
                                    </div>
                                    <div class="popular-item-stats">
                                        <span class="popular-item-orders">125 orders</span>
                                        <div class="popular-item-rating">
                                            <i class="fas fa-star"></i>
                                            <span>4.9</span>
                                        </div>
                                    </div>
                                </li>
                                <li class="popular-item">
                                    <img src="assets/images/egusi.jpg" alt="Egusi Soup" class="popular-item-image">
                                    <div class="popular-item-details">
                                        <h4>Egusi Soup</h4>
                                        <p>₦3,200</p>
                                    </div>
                                    <div class="popular-item-stats">
                                        <span class="popular-item-orders">98 orders</span>
                                        <div class="popular-item-rating">
                                            <i class="fas fa-star"></i>
                                            <span>4.8</span>
                                        </div>
                                    </div>
                                </li>
                                <li class="popular-item">
                                    <img src="assets/images/amala.jpg" alt="Amala" class="popular-item-image">
                                    <div class="popular-item-details">
                                        <h4>Amala</h4>
                                        <p>₦1,800</p>
                                    </div>
                                    <div class="popular-item-stats">
                                        <span class="popular-item-orders">87 orders</span>
                                        <div class="popular-item-rating">
                                            <i class="fas fa-star"></i>
                                            <span>4.7</span>
                                        </div>
                                    </div>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Orders Section -->
            <section id="orders-section" class="admin-section">
                <div class="admin-section-header">
                    <h2>Order Management</h2>
                    <div class="section-actions">
                        <div class="search-container">
                            <input type="text" placeholder="Search orders..." class="search-input">
                            <button class="search-btn"><i class="fas fa-search"></i></button>
                        </div>
                        <div class="filter-container">
                            <select class="filter-select">
                                <option value="all">All Orders</option>
                                <option value="pending">Pending</option>
                                <option value="processing">Processing</option>
                                <option value="completed">Completed</option>
                                <option value="cancelled">Cancelled</option>
                            </select>
                        </div>
                    </div>
                </div>

                <div class="orders-table-container">
                    <table class="data-table orders-table">
                        <thead>
                            <tr>
                                <th>
                                    <input type="checkbox" id="select-all-orders">
                                </th>
                                <th>Order ID</th>
                                <th>Customer</th>
                                <th>Items</th>
                                <th>Total</th>
                                <th>Date</th>
                                <th>Status</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td><input type="checkbox" class="order-checkbox"></td>
                                <td>#ORD-2305</td>
                                <td>
                                    <div class="customer-info">
                                        <span class="customer-name">John Doe</span>
                                        <span class="customer-email"><EMAIL></span>
                                    </div>
                                </td>
                                <td>3 items</td>
                                <td>₦12,500</td>
                                <td>May 15, 2023 10:45 AM</td>
                                <td><span class="status-badge completed">Completed</span></td>
                                <td>
                                    <div class="action-buttons">
                                        <button class="action-btn view-btn" title="View Order">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                        <button class="action-btn edit-btn" title="Edit Order">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <button class="action-btn delete-btn" title="Delete Order">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <td><input type="checkbox" class="order-checkbox"></td>
                                <td>#ORD-2304</td>
                                <td>
                                    <div class="customer-info">
                                        <span class="customer-name">Sarah Johnson</span>
                                        <span class="customer-email"><EMAIL></span>
                                    </div>
                                </td>
                                <td>2 items</td>
                                <td>₦8,750</td>
                                <td>May 15, 2023 9:30 AM</td>
                                <td><span class="status-badge processing">Processing</span></td>
                                <td>
                                    <div class="action-buttons">
                                        <button class="action-btn view-btn" title="View Order">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                        <button class="action-btn edit-btn" title="Edit Order">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <button class="action-btn delete-btn" title="Delete Order">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <div class="pagination">
                    <button class="pagination-btn prev-btn" disabled>
                        <i class="fas fa-chevron-left"></i> Previous
                    </button>
                    <div class="pagination-numbers">
                        <button class="pagination-number active">1</button>
                        <button class="pagination-number">2</button>
                        <button class="pagination-number">3</button>
                        <span class="pagination-ellipsis">...</span>
                        <button class="pagination-number">10</button>
                    </div>
                    <button class="pagination-btn next-btn">
                        Next <i class="fas fa-chevron-right"></i>
                    </button>
                </div>
            </section>

            <!-- Menu Management Section -->
            <section id="menu-section" class="admin-section">
                <div class="admin-section-header">
                    <h2>Menu Management</h2>
                    <div class="section-actions">
                        <button class="btn btn-primary" id="addMenuItem">
                            <i class="fas fa-plus"></i> Add New Item
                        </button>
                    </div>
                </div>

                <div class="menu-categories">
                    <div class="category-tabs">
                        <button class="category-tab active" data-category="all">All Items</button>
                        <button class="category-tab" data-category="local">Local Delights</button>
                        <button class="category-tab" data-category="international">International</button>
                        <button class="category-tab" data-category="beverages">Beverages</button>
                        <button class="category-tab" data-category="desserts">Desserts</button>
                    </div>

                    <div class="menu-items-grid">
                        <div class="menu-item-card">
                            <img src="assets/images/jollof-rice.jpg" alt="Jollof Rice" class="menu-item-image">
                            <div class="menu-item-content">
                                <h4 class="menu-item-name">Jollof Rice</h4>
                                <p class="menu-item-description">Traditional Nigerian rice dish with spices and vegetables</p>
                                <div class="menu-item-details">
                                    <span class="menu-item-price">₦2,500</span>
                                    <span class="menu-item-category">Local Delights</span>
                                </div>
                                <div class="menu-item-stats">
                                    <span class="menu-item-orders">125 orders</span>
                                    <div class="menu-item-rating">
                                        <i class="fas fa-star"></i>
                                        <span>4.9</span>
                                    </div>
                                </div>
                                <div class="menu-item-actions">
                                    <button class="action-btn edit-btn" title="Edit Item">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button class="action-btn delete-btn" title="Delete Item">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                    <button class="action-btn toggle-btn" title="Toggle Availability">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                </div>
                            </div>
                        </div>

                        <div class="menu-item-card">
                            <img src="assets/images/egusi.jpg" alt="Egusi Soup" class="menu-item-image">
                            <div class="menu-item-content">
                                <h4 class="menu-item-name">Egusi Soup</h4>
                                <p class="menu-item-description">Rich Nigerian soup made with ground melon seeds</p>
                                <div class="menu-item-details">
                                    <span class="menu-item-price">₦3,200</span>
                                    <span class="menu-item-category">Local Delights</span>
                                </div>
                                <div class="menu-item-stats">
                                    <span class="menu-item-orders">98 orders</span>
                                    <div class="menu-item-rating">
                                        <i class="fas fa-star"></i>
                                        <span>4.8</span>
                                    </div>
                                </div>
                                <div class="menu-item-actions">
                                    <button class="action-btn edit-btn" title="Edit Item">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button class="action-btn delete-btn" title="Delete Item">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                    <button class="action-btn toggle-btn" title="Toggle Availability">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                </div>
                            </div>
                        </div>

                        <div class="menu-item-card">
                            <img src="assets/images/amala.jpg" alt="Amala" class="menu-item-image">
                            <div class="menu-item-content">
                                <h4 class="menu-item-name">Amala</h4>
                                <p class="menu-item-description">Soft dough made from yam flour, served with various soups</p>
                                <div class="menu-item-details">
                                    <span class="menu-item-price">₦1,800</span>
                                    <span class="menu-item-category">Local Delights</span>
                                </div>
                                <div class="menu-item-stats">
                                    <span class="menu-item-orders">87 orders</span>
                                    <div class="menu-item-rating">
                                        <i class="fas fa-star"></i>
                                        <span>4.7</span>
                                    </div>
                                </div>
                                <div class="menu-item-actions">
                                    <button class="action-btn edit-btn" title="Edit Item">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button class="action-btn delete-btn" title="Delete Item">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                    <button class="action-btn toggle-btn" title="Toggle Availability">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Customers Section -->
            <section id="customers-section" class="admin-section">
                <div class="admin-section-header">
                    <h2>Customer Management</h2>
                    <div class="section-actions">
                        <div class="search-container">
                            <input type="text" placeholder="Search customers..." class="search-input">
                            <button class="search-btn"><i class="fas fa-search"></i></button>
                        </div>
                        <button class="btn btn-secondary">
                            <i class="fas fa-download"></i> Export
                        </button>
                    </div>
                </div>

                <div class="customers-table-container">
                    <table class="data-table customers-table">
                        <thead>
                            <tr>
                                <th>Customer</th>
                                <th>Email</th>
                                <th>Phone</th>
                                <th>Orders</th>
                                <th>Total Spent</th>
                                <th>Last Order</th>
                                <th>Status</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>
                                    <div class="customer-info">
                                        <div class="customer-avatar" style="background: #3498db; color: white; display: flex; align-items: center; justify-content: center; font-weight: bold;">JD</div>
                                        <span class="customer-name">John Doe</span>
                                    </div>
                                </td>
                                <td><EMAIL></td>
                                <td>+234 ************</td>
                                <td>15</td>
                                <td>₦187,500</td>
                                <td>May 15, 2023</td>
                                <td><span class="status-badge active">Active</span></td>
                                <td>
                                    <div class="action-buttons">
                                        <button class="action-btn view-btn" title="View Customer">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                        <button class="action-btn edit-btn" title="Edit Customer">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <button class="action-btn message-btn" title="Send Message">
                                            <i class="fas fa-envelope"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <td>
                                    <div class="customer-info">
                                        <div class="customer-avatar" style="background: #e74c3c; color: white; display: flex; align-items: center; justify-content: center; font-weight: bold;">SJ</div>
                                        <span class="customer-name">Sarah Johnson</span>
                                    </div>
                                </td>
                                <td><EMAIL></td>
                                <td>+234 802 345 6789</td>
                                <td>8</td>
                                <td>₦96,250</td>
                                <td>May 14, 2023</td>
                                <td><span class="status-badge active">Active</span></td>
                                <td>
                                    <div class="action-buttons">
                                        <button class="action-btn view-btn" title="View Customer">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                        <button class="action-btn edit-btn" title="Edit Customer">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <button class="action-btn message-btn" title="Send Message">
                                            <i class="fas fa-envelope"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </section>

            <!-- Analytics Section -->
            <section id="analytics-section" class="admin-section">
                <div class="admin-section-header">
                    <h2>Analytics & Reports</h2>
                    <div class="section-actions">
                        <div class="date-range-picker">
                            <button class="date-range-btn">
                                <i class="fas fa-calendar"></i>
                                <span>Last 30 Days</span>
                                <i class="fas fa-chevron-down"></i>
                            </button>
                        </div>
                        <button class="btn btn-secondary">
                            <i class="fas fa-download"></i> Export Report
                        </button>
                    </div>
                </div>

                <div class="analytics-overview">
                    <div class="analytics-card">
                        <div class="analytics-chart">
                            <h3>Sales Overview</h3>
                            <div class="chart-container">
                                <canvas id="salesChart"></canvas>
                            </div>
                        </div>
                    </div>
                    <div class="analytics-card">
                        <div class="analytics-chart">
                            <h3>Popular Categories</h3>
                            <div class="chart-container">
                                <canvas id="categoriesChart"></canvas>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="analytics-details">
                    <div class="analytics-card">
                        <div class="analytics-chart">
                            <h3>Customer Demographics</h3>
                            <div class="chart-container">
                                <canvas id="demographicsChart"></canvas>
                            </div>
                        </div>
                    </div>
                    <div class="analytics-card">
                        <div class="analytics-table">
                            <h3>Top Performing Items</h3>
                            <table class="data-table">
                                <thead>
                                    <tr>
                                        <th>Item</th>
                                        <th>Category</th>
                                        <th>Orders</th>
                                        <th>Revenue</th>
                                        <th>Rating</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td>Jollof Rice</td>
                                        <td>Local Delights</td>
                                        <td>125</td>
                                        <td>₦312,500</td>
                                        <td>4.9</td>
                                    </tr>
                                    <tr>
                                        <td>Egusi Soup</td>
                                        <td>Local Delights</td>
                                        <td>98</td>
                                        <td>₦313,600</td>
                                        <td>4.8</td>
                                    </tr>
                                    <tr>
                                        <td>Suya</td>
                                        <td>Local Delights</td>
                                        <td>87</td>
                                        <td>₦156,600</td>
                                        <td>4.7</td>
                                    </tr>
                                    <tr>
                                        <td>Chicken Alfredo</td>
                                        <td>International</td>
                                        <td>76</td>
                                        <td>₦266,000</td>
                                        <td>4.6</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Settings Section -->
            <section id="settings-section" class="admin-section">
                <div class="admin-section-header">
                    <h2>System Settings</h2>
                </div>

                <div class="settings-container">
                    <div class="settings-sidebar">
                        <ul class="settings-nav">
                            <li class="settings-nav-item active">
                                <a href="#general-settings" class="settings-nav-link">
                                    <i class="fas fa-cog"></i> General
                                </a>
                            </li>
                            <li class="settings-nav-item">
                                <a href="#user-settings" class="settings-nav-link">
                                    <i class="fas fa-users-cog"></i> User Management
                                </a>
                            </li>
                            <li class="settings-nav-item">
                                <a href="#payment-settings" class="settings-nav-link">
                                    <i class="fas fa-credit-card"></i> Payment Methods
                                </a>
                            </li>
                            <li class="settings-nav-item">
                                <a href="#notification-settings" class="settings-nav-link">
                                    <i class="fas fa-bell"></i> Notifications
                                </a>
                            </li>
                            <li class="settings-nav-item">
                                <a href="#backup-settings" class="settings-nav-link">
                                    <i class="fas fa-database"></i> Backup & Restore
                                </a>
                            </li>
                        </ul>
                    </div>

                    <div class="settings-content">
                        <div id="general-settings" class="settings-panel active">
                            <h3>General Settings</h3>
                            <form class="settings-form">
                                <div class="form-group">
                                    <label for="site-name">Restaurant Name</label>
                                    <input type="text" id="site-name" value="Magic Menu">
                                </div>
                                <div class="form-group">
                                    <label for="site-email">Contact Email</label>
                                    <input type="email" id="site-email" value="<EMAIL>">
                                </div>
                                <div class="form-group">
                                    <label for="site-phone">Contact Phone</label>
                                    <input type="tel" id="site-phone" value="+234 ************">
                                </div>
                                <div class="form-group">
                                    <label for="site-address">Restaurant Address</label>
                                    <textarea id="site-address">123 Main Street
City, State 12345</textarea>
                                </div>
                                <div class="form-group">
                                    <label for="currency">Currency</label>
                                    <select id="currency">
                                        <option value="NGN" selected>Nigerian Naira (₦)</option>
                                        <option value="USD">US Dollar ($)</option>
                                        <option value="EUR">Euro (€)</option>
                                        <option value="GBP">British Pound (£)</option>
                                    </select>
                                </div>
                                <div class="form-group">
                                    <label for="tax-rate">Tax Rate (%)</label>
                                    <input type="number" id="tax-rate" value="7.5">
                                </div>
                                <div class="form-actions">
                                    <button type="submit" class="btn btn-primary">Save Changes</button>
                                    <button type="reset" class="btn btn-secondary">Reset</button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </section>
        </main>
    </div>

    <!-- Add Menu Item Modal -->
    <div id="addMenuItemModal" class="admin-modal">
        <div class="admin-modal-content">
            <div class="admin-modal-header">
                <h3>Add New Menu Item</h3>
                <button class="modal-close">&times;</button>
            </div>
            <form id="addMenuItemForm" class="admin-form">
                <div class="form-group">
                    <label for="item-name">Item Name</label>
                    <input type="text" id="item-name" required>
                </div>
                <div class="form-group">
                    <label for="item-description">Description</label>
                    <textarea id="item-description" required></textarea>
                </div>
                <div class="form-row">
                    <div class="form-group">
                        <label for="item-price">Price (₦)</label>
                        <input type="number" id="item-price" min="0" step="0.01" required>
                    </div>
                    <div class="form-group">
                        <label for="item-category">Category</label>
                        <select id="item-category" required>
                            <option value="">Select Category</option>
                            <option value="local">Local Delights</option>
                            <option value="international">International</option>
                            <option value="beverages">Beverages</option>
                            <option value="desserts">Desserts</option>
                        </select>
                    </div>
                </div>
                <div class="form-group">
                    <label for="item-image">Item Image</label>
                    <div class="file-upload">
                        <input type="file" id="item-image" accept="image/*">
                        <label for="item-image" class="file-upload-label">
                            <i class="fas fa-cloud-upload-alt"></i> Choose Image
                        </label>
                        <div class="file-preview"></div>
                    </div>
                </div>
                <div class="form-group checkbox">
                    <input type="checkbox" id="item-featured">
                    <label for="item-featured">Featured Item</label>
                </div>
                <div class="form-actions">
                    <button type="submit" class="btn btn-primary">Add Item</button>
                    <button type="button" class="btn btn-secondary modal-cancel">Cancel</button>
                </div>
            </form>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script>
        // Simple test to verify script execution
        console.log('Admin page loaded');
    </script>
    <script src="assets/scripts/admin.js"></script>
</body>
</html>
