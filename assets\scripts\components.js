// components.js - Reusable UI components and utility functions

import { Security } from './utils/Security.js';

// Modal Component
class Modal {
    constructor(options = {}) {
        this.id = options.id || 'modal';
        this.title = options.title || '';
        this.content = options.content || '';
        this.size = options.size || 'medium'; // 'small', 'medium', 'large'
        this.onClose = options.onClose || null; // Callback function
        this.init();
    }

    init() {
        // Create modal elements
        const modal = document.createElement('div');
        modal.id = this.id;
        modal.className = `modal ${this.size}`;
        modal.setAttribute('role', 'dialog'); // Accessibility
        modal.setAttribute('aria-modal', 'true'); // Accessibility
        modal.setAttribute('aria-labelledby', 'modal-title');

        const modalContent = document.createElement('div');
        modalContent.className = 'modal-content';

        const modalHeader = document.createElement('div');
        modalHeader.className = 'modal-header';
        modalHeader.innerHTML = `<h3 id="modal-title">${this.title}</h3><span class="close-modal">×</span>`;

        const modalBody = document.createElement('div');
        modalBody.className = 'modal-body';
        modalBody.innerHTML = this.content;

        const modalFooter = document.createElement('div');
        modalFooter.className = 'modal-footer';
        modalFooter.innerHTML = '<button class="btn btn-secondary close-modal">Close</button>';

        // Assemble modal
        modalContent.appendChild(modalHeader);
        modalContent.appendChild(modalBody);
        modalContent.appendChild(modalFooter);
        modal.appendChild(modalContent);

        // Add to the document
        document.body.appendChild(modal);

        // Event listeners for closing
        modal.querySelectorAll('.close-modal').forEach(element => {
            element.addEventListener('click', () => this.close());
        });

        modal.addEventListener('click', (event) => {
            if (event.target === modal) {
                this.close();
            }
        });

         // Prevent scrolling of the body when modal is open
        modal.addEventListener('keydown', (e) => {
            if (e.key === 'Escape') {
                this.close();
            }
        });
    }

    open() {
        const modalElement = document.getElementById(this.id);
        if (modalElement) {
          modalElement.style.display = 'block';
          document.body.classList.add('modal-open'); // Prevent body scrolling
          // Focus management: move focus to the modal
          modalElement.querySelector('.modal-content').focus();
        }

    }

    close() {
         const modalElement = document.getElementById(this.id);
          if (modalElement) {
            modalElement.style.display = 'none';
            document.body.classList.remove('modal-open');
             if (this.onClose && typeof this.onClose === 'function') {
                this.onClose(); // Execute the callback if provided
             }
          }
    }
}

// Alert Component
class Alert {
    static show(message, type = 'info', duration = 3000) {
        // Check if an alert already exists, remove it
        const existingAlert = document.querySelector('.alert');
        if (existingAlert) {
            existingAlert.remove();
        }

        const alert = document.createElement('div');
        alert.className = `alert alert-${type}`; // Use template literals for dynamic class names
        alert.textContent = message;

        document.body.appendChild(alert);

        // Trigger the CSS animation
        setTimeout(() => {
            alert.classList.add('show');
        }, 10); // Small delay to ensure the class is added *after* insertion

        // Automatically remove the alert after the specified duration
        setTimeout(() => {
            alert.classList.remove('show');
            // Remove from DOM after the animation completes (0.3s in the CSS)
            setTimeout(() => {
                alert.remove();
            }, 300);

        }, duration);
    }
}

// Form Validation Utilities
class FormValidator {
    static VALIDATION_RULES = {
        email: {
            pattern: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
            message: 'Please enter a valid email address',
            sanitize: true
        },
        password: {
            pattern: Security.PASSWORD_PATTERN,
            message: 'Password must meet security requirements',
            sanitize: false // Don't sanitize passwords
        },
        text: {
            sanitize: true
        }
    };

    static async validateField(field) {
        const value = field.value.trim();
        const fieldType = field.dataset.validate;
        const rule = this.VALIDATION_RULES[fieldType];

        // Sanitize input if required
        if (rule?.sanitize) {
            field.value = Security.sanitizeInput(value);
        }

        // Special handling for password fields
        if (fieldType === 'password') {
            const validation = Security.validatePassword(value);
            if (!validation.isValid) {
                this.showError(field, validation.message);
                return false;
            }
        }

        return true;
    }

    static validateForm(form) {
        const formData = new FormData(form);
        return Security.validateAndSanitizeFormData(formData);
    }

    static validateRequiredFields(form) {
        let isValid = true;
        const requiredFields = form.querySelectorAll('[required]');

        requiredFields.forEach(field => {
            if (!this.validateField(field)) {
                isValid = false;
            }
        });

        return isValid;
    }

    static showError(formGroup, message) {
        formGroup.classList.add('has-error');
        formGroup.classList.remove('has-success');
        
        let errorDiv = formGroup.querySelector('.error-message');
        if (!errorDiv) {
            errorDiv = document.createElement('div');
            errorDiv.className = 'error-message';
            formGroup.appendChild(errorDiv);
        }
        errorDiv.textContent = message;
        
        // Add shake animation
        formGroup.classList.add('shake');
        setTimeout(() => formGroup.classList.remove('shake'), 500);
    }

    static showSuccess(formGroup) {
        formGroup.classList.remove('has-error');
        formGroup.classList.add('has-success');
        
        // Add success icon if not exists
        if (!formGroup.querySelector('.success-icon')) {
            const successIcon = document.createElement('span');
            successIcon.className = 'success-icon';
            successIcon.innerHTML = '✓';
            formGroup.appendChild(successIcon);
        }
    }

    static clearError(formGroup) {
        formGroup.classList.remove('has-error', 'has-success');
        const errorDiv = formGroup.querySelector('.error-message');
        const successIcon = formGroup.querySelector('.success-icon');
        
        if (errorDiv) errorDiv.remove();
        if (successIcon) successIcon.remove();
    }
}

// Cart Utilities
import { CURRENCY_CONFIG, formatPrice, validatePrice } from './config/currency.js';
import { ComponentErrorBoundary } from './utils/ComponentErrorBoundary.js';
import { ErrorBoundary } from './utils/ErrorBoundary.js';
import store from './store/index.js';
import { CART_ACTIONS, cartSelectors } from './store/cartStore.js';
import { api } from './api.js';
import { ApiErrorHandler } from './utils/ApiErrorHandler.js';

class CartManager {
    constructor() {
        this.errorBoundary = new ComponentErrorBoundary(this, {
            fallbackUI: `
                <div class="error-boundary-fallback">
                    <h3>Unable to load cart</h3>
                    <p>There was an error loading your shopping cart. Please try again.</p>
                    <button onclick="window.location.reload()" class="retry-button">
                        Reload Cart
                    </button>
                </div>
            `,
            onError: (error) => {
                Alert.show(ApiErrorHandler.getErrorMessage(error), 'error');
            }
        });

        // Fix the subscription callbacks
        store.subscribe(cartSelectors.getItemCount, (count) => {
            this.updateCartCount(count);
        });

        store.subscribe(
            (state) => state.cart.isLoading,
            (isLoading) => {
                this.handleLoadingState(isLoading);
            }
        );
    }

    handleLoadingState(isLoading) {
        const cartContainer = document.querySelector('.cart-container');
        if (cartContainer) {
            if (isLoading) {
                Loader.show({ container: cartContainer });
            } else {
                Loader.hide();
            }
        }
    }

    async calculateTotals(items) {
        return this.errorBoundary.execute(async () => {
            try {
                const subtotal = items.reduce((sum, item) => {
                    const itemPrice = validatePrice(item.price);
                    const quantity = Math.max(1, parseInt(item.quantity) || 1);
                    return sum + (itemPrice * quantity);
                }, 0);

                const tax = subtotal * CURRENCY_CONFIG.vatRate;
                const deliveryFee = CURRENCY_CONFIG.deliveryFee;
                const total = subtotal + tax + deliveryFee;

                return {
                    subtotal,
                    tax,
                    deliveryFee,
                    total
                };
            } catch (error) {
                throw new Error(`Error calculating cart totals: ${error.message}`);
            }
        });
    }
    
    // Add method to handle quantity validation
    static validateQuantity(quantity, maxQuantity = 99) {
        return Math.max(1, Math.min(parseInt(quantity) || 1, maxQuantity));
    }

    static validateCartItem(item) {
        if (!item.price) {
            throw new Error('Item price is required');
        }
        try {
            validatePrice(item.price);
        } catch (error) {
            throw new Error(`Invalid item price: ${error.message}`);
        }
    }

    updateCartCount(count) {
        const cartCountElements = document.querySelectorAll('.cart-count');
        cartCountElements.forEach(element => {
            element.textContent = count;
        });
    }

    static getCartItems() {
        return cartSelectors.getItems(store.getState());
    }

    static getTotals() {
        return cartSelectors.getTotals(store.getState());
    }

    static clearCart() {
        store.dispatch({ type: CART_ACTIONS.CLEAR_CART });
    }

    async addToCart(item) {
        const addToCartBtn = document.querySelector(`[data-item-id="${item.id}"]`);
        
        try {
            if (addToCartBtn) {
                Loader.showButtonLoader(addToCartBtn, 'Adding...');
            }

            await api.addToCart(item.id, item.quantity);
            
            store.dispatch({
                type: CART_ACTIONS.ADD_ITEM,
                payload: item
            });

            Alert.show('Item added to cart', 'success');
        } catch (error) {
            Alert.show(ApiErrorHandler.handleError(error, 'add_to_cart'), 'error');
        } finally {
            if (addToCartBtn) {
                Loader.hideButtonLoader(addToCartBtn);
            }
        }
    }

    static removeFromCart(itemId) {
        store.dispatch({
            type: CART_ACTIONS.REMOVE_ITEM,
            payload: itemId
        });
    }

    async updateQuantity(itemId, quantity) {
        const quantityContainer = document.querySelector(`.cart-item[data-id="${itemId}"] .quantity-controls`);
        
        try {
            if (quantityContainer) {
                Loader.show({
                    container: quantityContainer,
                    inline: true,
                    size: 'small'
                });
            }

            await api.put(`/api/cart/items/${itemId}`, { quantity });
            
            store.dispatch({
                type: CART_ACTIONS.UPDATE_QUANTITY,
                payload: { id: itemId, quantity }
            });
        } catch (error) {
            Alert.show(ApiErrorHandler.handleError(error, 'update_quantity'), 'error');
        } finally {
            if (quantityContainer) {
                Loader.hide();
            }
        }
    }

    static formatItemVariations(variations) {
        if (!variations || Object.keys(variations).length === 0) return '';
        
        return Object.entries(variations)
            .map(([key, options]) => {
                const optionsStr = Array.isArray(options) ? options.join(', ') : options;
                return `${key}: ${optionsStr}`;
            })
            .join(' | ');
    }

    static handleCartError(error) {
        ErrorBoundary.logError(error, {
            component: 'CartManager',
            context: 'cart_operation'
        });
        
        return ErrorBoundary.renderFallbackUI(`
            <div class="error-boundary-fallback">
                <p>Unable to process cart operation. Please try again.</p>
            </div>
        `);
    }
}

// Loading Spinner Component (Optional, good for longer operations)
class Loader {
    static show(options = {}) {
        const {
            container = document.body,
            inline = false,
            size = 'medium', // small, medium, large
            text = ''
        } = options;

        const loader = document.createElement('div');
        loader.className = `loader ${inline ? 'loader-inline' : 'loader-overlay'} loader-${size}`;
        
        loader.innerHTML = `
            <div class="spinner"></div>
            ${text ? `<div class="loader-text">${text}</div>` : ''}
        `;

        if (inline) {
            // For inline loaders, clear the container first
            container.innerHTML = '';
        }
        
        container.appendChild(loader);
        return loader;
    }

    static hide(loader) {
        if (loader) {
            loader.remove();
        } else {
            document.querySelectorAll('.loader').forEach(l => l.remove());
        }
    }

    static showButtonLoader(button, text = 'Loading...') {
        button.disabled = true;
        button.dataset.originalText = button.innerHTML;
        button.innerHTML = `
            <div class="btn-loader"></div>
            <span>${text}</span>
        `;
        button.classList.add('loading');
    }

    static hideButtonLoader(button) {
        button.disabled = false;
        button.innerHTML = button.dataset.originalText;
        button.classList.remove('loading');
        delete button.dataset.originalText;
    }
}
// Utility Functions
class Utils {
    static formatCurrency(amount) {
        return new Intl.NumberFormat('en-NG', {
            style: 'currency',
            currency: 'NGN',
            minimumFractionDigits: 2,
            maximumFractionDigits: 2
        }).format(amount);
    }

    static formatDate(date) {
       return new Intl.DateTimeFormat('en-US',{
            dateStyle: 'long',
            timeStyle: 'short',
       }).format(date);
    }

    // Debounce function to limit the rate of function calls
    static debounce(func, wait) {
        let timeout;
        return function(...args) {
            const context = this;
            clearTimeout(timeout);
            timeout = setTimeout(() => func.apply(context, args), wait);
        };
    }

    // Throttle function to limit the frequency of function calls
    static throttle(func, limit) {
        let inThrottle;
        return function(...args) {
            const context = this;
            if (!inThrottle) {
                func.apply(context, args);
                inThrottle = true;
                setTimeout(() => inThrottle = false, limit);
            }
        };
    }
}

// Cart Sidebar Component
class CartSidebar {
    constructor() {
        this.sidebar = document.querySelector('.cart-sidebar');
        this.overlay = document.querySelector('.cart-overlay');
        this.itemsContainer = this.sidebar?.querySelector('.cart-items');
        this.closeButton = this.sidebar?.querySelector('.close-cart-sidebar');
        
        this.initialize();
        
        // Subscribe to cart updates
        store.subscribe(cartSelectors.getItems, () => this.updateDisplay());
    }

    initialize() {
        // Add event listeners
        this.closeButton?.addEventListener('click', () => this.close());
        this.overlay?.addEventListener('click', () => this.close());

        // Event delegation for cart item interactions
        this.sidebar?.addEventListener('click', (event) => {
            const target = event.target;
            
            if (target.closest('.decrease')) {
                const itemId = target.closest('.decrease').dataset.id;
                this.updateItemQuantity(itemId, -1);
            } else if (target.closest('.increase')) {
                const itemId = target.closest('.increase').dataset.id;
                this.updateItemQuantity(itemId, 1);
            } else if (target.closest('.remove-item-btn')) {
                const itemId = target.closest('.remove-item-btn').dataset.id;
                this.removeItem(itemId);
            }
        });

        this.checkoutBtn?.addEventListener('click', () => {
            window.location.href = 'checkout.html';
        });
    }

    open() {
        this.sidebar?.classList.add('open');
        this.overlay?.classList.add('open');
        document.body.classList.add('cart-sidebar-open');
        this.updateDisplay();
    }

    close() {
        this.sidebar?.classList.remove('open');
        this.overlay?.classList.remove('open');
        document.body.classList.remove('cart-sidebar-open');
    }

    updateDisplay() {
        const cartItems = CartManager.getCartItems();
        const totals = CartManager.getTotals();

        if (!this.itemsContainer) return;
        this.itemsContainer.innerHTML = '';

        if (cartItems.length === 0) {
            this.itemsContainer.innerHTML = '<p class="cart-empty-message">Your cart is empty.</p>';
            return;
        }

        // Render cart items
        cartItems.forEach(item => {
            this.itemsContainer.innerHTML += `
                <div class="cart-item" data-id="${item.id}">
                    <img src="${item.image}" alt="${item.name}">
                    <div class="item-details">
                        <h3>${item.name}</h3>
                        <p class="price">${Utils.formatCurrency(item.price)}</p>
                        <div class="quantity-controls">
                            <button class="decrease" data-id="${item.id}">-</button>
                            <span class="quantity">${item.quantity}</span>
                            <button class="increase" data-id="${item.id}">+</button>
                        </div>
                    </div>
                    <button class="remove-item-btn" data-id="${item.id}">&times;</button>
                </div>
            `;
        });

        // Update totals
        this.sidebar.querySelector('.subtotal').textContent = Utils.formatCurrency(totals.subtotal);
        this.sidebar.querySelector('.tax').textContent = Utils.formatCurrency(totals.tax);
        this.sidebar.querySelector('.delivery').textContent = Utils.formatCurrency(totals.deliveryFee);
        this.sidebar.querySelector('.total-amount').textContent = Utils.formatCurrency(totals.total);
    }

    createItemElement(item) {
        const div = document.createElement('div');
        div.className = 'cart-item';
        div.innerHTML = `
            <div class="item-image">
                <img src="assets/images/placeholder.jpg" alt="${item.name}" loading="lazy">
            </div>
            <div class="item-details">
                <h3>${item.name}</h3>
                <p>${Utils.formatCurrency(item.price)}</p>
                <div class="quantity-controls">
                    <button class="quantity-btn decrease" data-id="${item.id}">-</button>
                    <span class="quantity">${item.quantity}</span>
                    <button class="quantity-btn increase" data-id="${item.id}">+</button>
                </div>
            </div>
            <button class="remove-item" data-id="${item.id}">×</button>
        `;

        // Add event listeners
        div.querySelector('.decrease').addEventListener('click', () => 
            this.updateItemQuantity(item.id, -1));
        div.querySelector('.increase').addEventListener('click', () => 
            this.updateItemQuantity(item.id, 1));
        div.querySelector('.remove-item').addEventListener('click', () => 
            this.removeItem(item.id));

        return div;
    }

    updateSummary(subtotal) {
        const { tax, deliveryFee, total } = CartManager.calculateTotals([{ price: subtotal, quantity: 1 }]);

        this.sidebar.querySelector('.subtotal').textContent = Utils.formatCurrency(subtotal);
        this.sidebar.querySelector('.tax').textContent = Utils.formatCurrency(tax);
        this.sidebar.querySelector('.delivery').textContent = Utils.formatCurrency(deliveryFee);
        this.sidebar.querySelector('.total-amount').textContent = Utils.formatCurrency(total);
    }

    updateItemQuantity(itemId, change) {
        const cartItems = CartManager.getCartItems();
        const updatedCart = cartItems.map(item => {
            if (item.id === itemId) {
                const newQuantity = Math.max(1, item.quantity + change);
                return { ...item, quantity: newQuantity };
            }
            return item;
        });

        CartManager.saveCartItems(updatedCart);
        this.updateDisplay();
        window.dispatchEvent(new CustomEvent('cartUpdated'));
    }

    removeItem(itemId) {
        const updatedCart = CartManager.getCartItems().filter(item => item.id !== itemId);
        CartManager.saveCartItems(updatedCart);
        this.updateDisplay();
        window.dispatchEvent(new CustomEvent('cartUpdated'));
    }
}

// Export the components and utilities
export {
    Modal,
    Alert,
    FormValidator,
    CartManager,
    Loader,
    Utils,
    CartSidebar
};
