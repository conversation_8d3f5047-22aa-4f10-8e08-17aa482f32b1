export default class MenuDetail {
    constructor() {
        console.log('MenuDetail initialized'); // Debug log
        this.currentItem = null;
        this.initializeModal();
    }

    initializeModal() {
        console.log('Initializing modal'); // Debug log
        
        // Create modal if it doesn't exist
        if (!document.getElementById('itemDetailModal')) {
            console.log('Creating modal element'); // Debug log
            const modalHTML = `
                <div id="itemDetailModal" class="modal">
                    <div class="modal-content">
                        <span class="close-modal">&times;</span>
                        <div class="modal-body">
                            <div class="modal-image-container">
                                <img id="modalImage" src="" alt="">
                            </div>
                            <div class="modal-info">
                                <h3 id="modalTitle"></h3>
                                <p id="modalDescription"></p>
                                <p class="price" id="modalPrice"></p>
                                
                                <div class="nutrition-info">
                                    <h4>Nutrition Information</h4>
                                    <p>Calories: <span id="modalCalories"></span></p>
                                    <p>Protein: <span id="modalProtein"></span></p>
                                    <p>Carbs: <span id="modalCarbs"></span></p>
                                </div>
                                
                                <div class="ingredients">
                                    <h4>Ingredients</h4>
                                    <p id="modalIngredients"></p>
                                </div>
                                
                                <div class="quantity-control">
                                    <button class="quantity-btn decrease">-</button>
                                    <input type="number" id="modalQuantity" value="1" min="1" max="10">
                                    <button class="quantity-btn increase">+</button>
                                </div>
                                
                                <button id="modalAddToCart" class="btn btn-primary">Add to Cart</button>
                            </div>
                        </div>
                    </div>
                </div>
            `;
            document.body.insertAdjacentHTML('beforeend', modalHTML);
        }

        // Initialize modal elements
        this.modal = document.getElementById('itemDetailModal');
        this.modalImage = document.getElementById('modalImage');
        this.modalTitle = document.getElementById('modalTitle');
        this.modalDescription = document.getElementById('modalDescription');
        this.modalPrice = document.getElementById('modalPrice');
        this.modalCalories = document.getElementById('modalCalories');
        this.modalProtein = document.getElementById('modalProtein');
        this.modalCarbs = document.getElementById('modalCarbs');
        this.modalIngredients = document.getElementById('modalIngredients');
        this.modalQuantity = document.getElementById('modalQuantity');
        this.modalAddToCart = document.getElementById('modalAddToCart');

        this.initializeEventListeners();
    }

    initializeEventListeners() {
        // Close modal when clicking the X or outside the modal
        const closeBtn = this.modal.querySelector('.close-modal');
        closeBtn.addEventListener('click', () => this.closeModal());
        
        window.addEventListener('click', (e) => {
            if (e.target === this.modal) {
                this.closeModal();
            }
        });

        // Quantity controls
        const decreaseBtn = this.modal.querySelector('.quantity-btn.decrease');
        const increaseBtn = this.modal.querySelector('.quantity-btn.increase');
        
        decreaseBtn.addEventListener('click', () => {
            const currentVal = parseInt(this.modalQuantity.value);
            if (currentVal > 1) this.modalQuantity.value = currentVal - 1;
        });

        increaseBtn.addEventListener('click', () => {
            const currentVal = parseInt(this.modalQuantity.value);
            if (currentVal < 10) this.modalQuantity.value = currentVal + 1;
        });

        // Add to cart
        this.modalAddToCart.addEventListener('click', () => this.addToCart());
    }

    openModal(itemData) {
        console.log('Opening modal with data:', itemData); // Debug log
        this.currentItem = itemData;

        // Update modal content
        this.modalImage.src = itemData.image;
        this.modalImage.alt = itemData.title;
        this.modalTitle.textContent = itemData.title;
        this.modalDescription.textContent = itemData.description;
        this.modalPrice.textContent = itemData.price;
        this.modalCalories.textContent = itemData.nutrition.calories;
        this.modalProtein.textContent = itemData.nutrition.protein;
        this.modalCarbs.textContent = itemData.nutrition.carbs;
        this.modalIngredients.textContent = itemData.ingredients.join(', ');

        // Reset quantity
        this.modalQuantity.value = 1;

        // Show modal
        this.modal.style.display = 'block';
        document.body.style.overflow = 'hidden'; // Prevent scrolling
    }

    closeModal() {
        this.modal.style.display = 'none';
        document.body.style.overflow = ''; // Restore scrolling
    }

    addToCart() {
        try {
            if (!this.currentItem) {
                throw new Error('No item selected');
            }
            
            const quantity = parseInt(this.modalQuantity.value) || 1;
            const item = {
                id: this.currentItem.id,
                name: this.currentItem.title,
                price: parseFloat(this.currentItem.price.replace(/[^0-9.-]+/g,"")),
                quantity: quantity
            };
            
            // Get existing cart items
            const cartItems = JSON.parse(localStorage.getItem('cartItems') || '[]');
            
            // Check if item already exists in cart
            const existingItemIndex = cartItems.findIndex(i => i.id === item.id);
            
            if (existingItemIndex > -1) {
                cartItems[existingItemIndex].quantity += quantity;
            } else {
                cartItems.push(item);
            }
            
            // Save updated cart
            localStorage.setItem('cartItems', JSON.stringify(cartItems));
            
            // Show success message
            Alert.show('Item added to cart', 'success');
            
            // Close modal
            this.closeModal();
            
        } catch (error) {
            console.error('Add to cart error:', error);
            Alert.show('Failed to add item to cart', 'error');
        }
    }
}
